<?xml version="1.0" encoding="utf-8"?>
<TpsData xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <Name>MaterialX</Name>
  <Location>Engine/Source/ThirdParty/MaterialX</Location>
  <Function>MaterialX is an open standard for transfer of rich material and look-development content between applications and renderers.</Function>
  <Eula>https://github.com/AcademySoftwareFoundation/MaterialX/blob/v1.38.10/LICENSE</Eula>
  <RedistributeTo>
    <EndUserGroup>Licensees</EndUserGroup>
    <EndUserGroup>Git</EndUserGroup>
    <EndUserGroup>P4</EndUserGroup>
  </RedistributeTo>
  <LicenseFolder>Engine/Source/ThirdParty/Licenses</LicenseFolder>
</TpsData>
