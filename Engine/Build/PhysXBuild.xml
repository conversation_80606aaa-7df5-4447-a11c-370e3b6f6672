<?xml version='1.0' ?>
<BuildGraph xmlns="http://www.epicgames.com/BuildGraph" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.epicgames.com/BuildGraph ../../Engine/Build/Graph/Schema.xsd" >
	<EnvVar Name="COMMANDER_JOBID"/>
	<Option Name="PreflightChange" DefaultValue="" Description="Preflight CL number if preflight, empty otherwise"/>
	
	<Option Name="SkipBuildSolutions" DefaultValue="false" Description="If we don't want to build solutions"/>
	<Option Name="SkipBuild" DefaultValue="false" Description="Skip build"/>
	<Option Name="SkipCreateChangelist" DefaultValue="false" Description="Whether or not to make a changelist"/>
	<Option Name="SkipSubmit" DefaultValue="false" Description="Whether or not to submit changelist"/>
	<Option Name="Robomerge" DefaultValue="Merge" Description="Robomerge action for this build, defaults to Merge"/>
	<Option Name="TargetLibs" DefaultValue="PhysX+APEX" Description="Target libs"/>
	<Option Name="TargetConfigs" DefaultValue="profile+release+checked" Description="Target Configs"/>
	<Option Name="TargetWindowsCompilers" DefaultValue="VisualStudio2022" Description="Target Compilers"/>
	<Option Name="WithWin64" DefaultValue="false" Description="Compile for Win64"/>
	<Option Name="WithPS4" DefaultValue="false" Description="Compile for PS4"/>
	<Option Name="WithLinux" DefaultValue="false" Description="Compile for Linux"/>
	<Option Name="WithLinux-x86" DefaultValue="false" Description="Compile for Linux x86"/>
	<Option Name="WithLinux-ARM32" DefaultValue="false" Description="Compile for Linux ARM32"/>
	<Option Name="WithLinux-ARM64" DefaultValue="false" Description="Compile for Linux ARM64"/>
	<Option Name="WithMac" DefaultValue="false" Description="Compile for Mac"/>
	<Option Name="WithIOS" DefaultValue="false" Description="Compile for IOS"/>
	<Option Name="WithTVOS" DefaultValue="false" Description="Compile for TVOS"/>
	<Option Name="WithAndroid-ARMv7" DefaultValue="false" Description="Compile for Android ARMv7"/>
	<Option Name="WithAndroid-ARM64" DefaultValue="false" Description="Compile for Android ARM64"/>
	<Option Name="WithAndroid-x86" DefaultValue="false" Description="Compile for Android x86"/>
	<Option Name="WithAndroid-x64" DefaultValue="false" Description="Compile for Android x64"/>
	<Option Name="WithSwitch" DefaultValue="false" Description="Compile for Switch"/>
	<Option Name="ShelvedChange" DefaultValue="" Description="Change to unshelve"/>

	<Property Name="OtherArgs" Value=""/>
	<Property Name="OtherArgs" Value="$(OtherArgs) -Unshelve=$(ShelvedChange)" If="'$(ShelvedChange)' != ''"/>
	<Property Name="OtherArgs" Value="$(OtherArgs) -SkipBuildSolutions" If="$(SkipBuildSolutions)"/>
	<Property Name="OtherArgs" Value="$(OtherArgs) -SkipBuild" If="$(SkipBuild)"/>
	<Property Name="OtherArgs" Value="$(OtherArgs) -SkipCreateChangelist" If="$(SkipCreateChangelist)"/>
	<Property Name="OtherArgs" Value="$(OtherArgs) -SkipSubmit" If="$(SkipSubmit)"/>
	<Property Name="OtherArgs" Value="$(OtherArgs) -Robomerge=$(Robomerge)" If="'$(Robomerge)' != ''"/>
	
	<Agent Name="PhysX Win64" Type="EngineWin64;PhysX_CompileWin64;Win64">
		<Node Name="Compile PhysX Win64">
			<Command Name="BuildPhysX" Arguments="-TargetPlatforms=Win64 -TargetConfigs=&quot;$(TargetConfigs)&quot; -TargetWindowsCompilers=&quot;$(TargetWindowsCompilers)&quot; $(OtherArgs)"/>
		</Node>
	</Agent>
	<!-- @MIXEDREALITY_CHANGE : BEGIN ARM -->
	<Agent Name="PhysX PS4" Type="EngineWin64;PhysX_CompileWin64;Win64">
		<Node Name="Compile PhysX PS4">
			<Command Name="BuildPhysX" Arguments="-TargetPlatforms=PS4 -TargetConfigs=&quot;$(TargetConfigs)&quot; -TargetWindowsCompilers=&quot;$(TargetWindowsCompilers)&quot; $(OtherArgs)"/>
		</Node>
	</Agent>
	<Agent Name="PhysX Linux" Type="EngineWin64;PhysX_CompileWin64;Win64">
		<Node Name="Compile PhysX Linux">
			<Command Name="BuildPhysX" Arguments="-TargetPlatforms=Linux -TargetConfigs=&quot;$(TargetConfigs)&quot; -TargetWindowsCompilers=&quot;$(TargetWindowsCompilers)&quot; $(OtherArgs)"/>
		</Node>
	</Agent>
	<Agent Name="PhysX Linux x86" Type="EngineWin64;PhysX_CompileWin64;Win64">
		<Node Name="Compile PhysX Linux x86">
			<Command Name="BuildPhysX" Arguments="-TargetPlatforms=Linux-i686-unknown-linux-gnu -TargetConfigs=&quot;$(TargetConfigs)&quot; -TargetWindowsCompilers=&quot;$(TargetWindowsCompilers)&quot; $(OtherArgs)"/>
		</Node>
	</Agent>
	<Agent Name="PhysX Linux ARM64" Type="EngineWin64;PhysX_CompileWin64;Win64">
		<Node Name="Compile PhysX Linux ARM64">
			<Command Name="BuildPhysX" Arguments="-TargetPlatforms=Linux-aarch64-unknown-linux-gnueabi -TargetConfigs=&quot;$(TargetConfigs)&quot; -TargetWindowsCompilers=&quot;$(TargetWindowsCompilers)&quot; $(OtherArgs)"/>
		</Node>
	</Agent>
	<Agent Name="PhysX Android Arm64" Type="EngineWin64;PhysX_CompileWin64;Win64">
		<Node Name="Compile PhysX Android Arm64">
			<Command Name="BuildPhysX" Arguments="-TargetPlatforms=Android-arm64 -TargetConfigs=&quot;$(TargetConfigs)&quot; -TargetWindowsCompilers=&quot;$(TargetWindowsCompilers)&quot; $(OtherArgs)"/>
		</Node>
	</Agent>
	<Agent Name="PhysX Android x86" Type="EngineWin64;PhysX_CompileWin64;Win64">
		<Node Name="Compile PhysX Android x86">
			<Command Name="BuildPhysX" Arguments="-TargetPlatforms=Android-x86 -TargetConfigs=&quot;$(TargetConfigs)&quot; -TargetWindowsCompilers=&quot;$(TargetWindowsCompilers)&quot; $(OtherArgs)"/>
		</Node>
	</Agent>
	<Agent Name="PhysX Android x64" Type="EngineWin64;PhysX_CompileWin64;Win64">
		<Node Name="Compile PhysX Android x64">
			<Command Name="BuildPhysX" Arguments="-TargetPlatforms=Android-x64 -TargetConfigs=&quot;$(TargetConfigs)&quot; -TargetWindowsCompilers=&quot;$(TargetWindowsCompilers)&quot; $(OtherArgs)"/>
		</Node>
	</Agent>
	<Agent Name="PhysX Mac" Type="EngineMac;PhysX_CompileMac;Mac">
		<Node Name="Compile PhysX Mac">
			<Command Name="BuildPhysX" Arguments="-TargetPlatforms=Mac -TargetConfigs=&quot;$(TargetConfigs)&quot; $(OtherArgs)"/>
		</Node>
	</Agent>
	<Agent Name="PhysX IOS" Type="EngineMac;PhysX_CompileMac;Mac">
		<Node Name="Compile PhysX IOS">
			<Command Name="BuildPhysX" Arguments="-TargetPlatforms=IOS -TargetConfigs=&quot;$(TargetConfigs)&quot; $(OtherArgs)"/>
		</Node>
	</Agent>
	<Agent Name="PhysX TVOS" Type="EngineMac;PhysX_CompileMac;Mac">
		<Node Name="Compile PhysX TVOS">
			<Command Name="BuildPhysX" Arguments="-TargetPlatforms=TVOS -TargetConfigs=&quot;$(TargetConfigs)&quot; $(OtherArgs)"/>
		</Node>
	</Agent>
	<Agent Name="PhysX Switch" Type="EngineWin64;PhysX_CompileWin64;Win64">
		<Node Name="Compile PhysX Switch">
			<Command Name="BuildPhysX" Arguments="-TargetPlatforms=Switch -TargetConfigs=&quot;$(TargetConfigs)&quot; $(OtherArgs)"/>
		</Node>
	</Agent>

	<Property Name="PhysXTargets" Value=""/>
	<Property Name="PhysXTargets" Value="$(PhysXTargets);Compile PhysX Win64" If="$(WithWin64)"/>
	<Property Name="PhysXTargets" Value="$(PhysXTargets);Compile PhysX PS4" If="$(WithPS4)"/>
	<Property Name="PhysXTargets" Value="$(PhysXTargets);Compile PhysX Linux" If="$(WithLinux)"/>
	<Property Name="PhysXTargets" Value="$(PhysXTargets);Compile PhysX Linux x86" If="$(WithLinux-x86)"/>
	<Property Name="PhysXTargets" Value="$(PhysXTargets);Compile PhysX Linux ARM32" If="$(WithLinux-ARM32)"/>
	<Property Name="PhysXTargets" Value="$(PhysXTargets);Compile PhysX Linux ARM64" If="$(WithLinux-ARM64)"/>
	<Property Name="PhysXTargets" Value="$(PhysXTargets);Compile PhysX Android Arm64" If="$(WithAndroid-ARM64)"/>
	<Property Name="PhysXTargets" Value="$(PhysXTargets);Compile PhysX Android x64" If="$(WithAndroid-x64)"/>
	<Property Name="PhysXTargets" Value="$(PhysXTargets);Compile PhysX Mac" If="$(WithMac)"/>
	<Property Name="PhysXTargets" Value="$(PhysXTargets);Compile PhysX IOS" If="$(WithIOS)"/>
	<Property Name="PhysXTargets" Value="$(PhysXTargets);Compile PhysX TVOS" If="$(WithTVOS)"/>
	<Property Name="PhysXTargets" Value="$(PhysXTargets);Compile PhysX Switch" If="$(WithSwitch)"/>
	
	<Aggregate Name="Build PhysX" Requires="$(PhysXTargets)"/>
	
	<Property Name="AllEmailTargets" Value="<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>"/>
	
	<Notify Nodes="Compile PhysX Win64" Users="$(AllEmailTargets)"/>
	<Notify Nodes="Compile PhysX PS4" Users="$(AllEmailTargets);<EMAIL>"/>
	<Notify Nodes="Compile PhysX Linux;Compile PhysX Linux x86;Compile PhysX Linux ARM32;Compile PhysX Linux ARM64" Users="$(AllEmailTargets);<EMAIL>"/>
	<Notify Nodes="Compile PhysX Android Armv7;Compile PhysX Android Arm64;Compile PhysX Android x86;Compile PhysX Android x64" Users="$(AllEmailTargets);<EMAIL>"/>
	<Notify Nodes="Compile PhysX Mac;Compile PhysX IOS;Compile PhysX TVOS" Users="$(AllEmailTargets);<EMAIL>"/>
	<Notify Nodes="Compile PhysX Switch" Users="$(AllEmailTargets);<EMAIL>"/>
</BuildGraph>
