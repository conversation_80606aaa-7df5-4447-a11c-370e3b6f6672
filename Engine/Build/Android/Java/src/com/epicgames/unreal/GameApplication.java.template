package com.epicgames.unreal;

import android.app.Application;
import android.content.Context;
import android.content.res.Configuration;
import androidx.lifecycle.Lifecycle;
import androidx.lifecycle.LifecycleObserver;
import androidx.lifecycle.OnLifecycleEvent;
import androidx.lifecycle.ProcessLifecycleOwner;

import com.epicgames.unreal.network.NetworkChangedManager;
//$${gameApplicationImportAdditions}$$

public class GameApplication extends Application implements LifecycleObserver {
	private static final Logger Log = new Logger("UE", "GameApp");

	private static boolean isForeground = false;

	private static Context context;

//$${gameApplicationClassAdditions}$$

	@Override
	public void onCreate() {
		super.onCreate();
		GameApplication.context = getApplicationContext();
//$${gameApplicationOnCreateAdditions}$$

		ProcessLifecycleOwner.get().getLifecycle().addObserver(this);

		// get the process name to see if we need network observer
		String processName = "unknown";
		if (android.os.Build.VERSION.SDK_INT >= 28)
		{
			processName = Application.getProcessName();
		}
		else
		{
			try
			{
				Class<?> activityThread = Class.forName("android.app.ActivityThread");
				processName = (String)activityThread.getDeclaredMethod("currentProcessName").invoke(null);
			}
			catch (Exception e)
			{
				// ignore
			}
		}
		
		if (!processName.contains("psoprogramservice"))
		{
			NetworkChangedManager.getInstance().initNetworkCallback(this);
		}
	}

	public static Context getAppContext() {
		return GameApplication.context;
	}

	@Override
	public void attachBaseContext(Context base) {
		super.attachBaseContext(base);
//$${gameApplicationAttachBaseContextAdditions}$$
	}

	@Override
	public void onLowMemory() {
		super.onLowMemory();
//$${gameApplicationOnLowMemoryAdditions}$$
	}

	@Override
	public void onTrimMemory(int level) {
		super.onTrimMemory(level);
//$${gameApplicationOnTrimMemoryAdditions}$$
	}

	@Override
	public void onConfigurationChanged (Configuration newConfig) {
		super.onConfigurationChanged(newConfig);
//$${gameApplicationOnConfigurationChangedAdditions}$$
	}

	@OnLifecycleEvent(Lifecycle.Event.ON_START)
	void onEnterForeground() {
		Log.verbose("App in foreground");
		isForeground = true;
	}

	@OnLifecycleEvent(Lifecycle.Event.ON_STOP)
	void onEnterBackground() {
		Log.verbose("App in background");
		isForeground = false;
	}

	@SuppressWarnings("unused")
	public static boolean isAppInForeground() {
		return isForeground;
	}

	public static boolean isAppInBackground() {
		return !isForeground;
	}
}
