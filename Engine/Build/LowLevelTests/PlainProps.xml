<?xml version="1.0" encoding="utf-8"?>
<BuildGraph xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.epicgames.com/BuildGraph ../../Build/Graph/Schema.xsd" xmlns="http://www.epicgames.com/BuildGraph">
  <Option Name="RunPlainPropsTests" DefaultValue="" Description="Run PlainProps Tests" />
  <Property Name="TestNames" Value="$(TestNames);PlainProps" />
  <Extend Name="RunAllTests">
    <Expand Name="DeployAndTest" Platform="Win64" TestName="PlainProps" ShortName="PlainProps" TargetName="PlainPropsTests" BinaryRelativePath="Engine\Binaries" ReportType="xml" ExtraCompilationArgs="-allmodules" Deactivated="True" />
  </Extend>
</BuildGraph>